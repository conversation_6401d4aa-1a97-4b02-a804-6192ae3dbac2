/**
 * 图像导航相关逻辑（拖拽、平移等）
 */

import { ref, onMounted, onUnmounted } from 'vue'
import * as fabric from 'fabric'
import type { FabricCanvas } from '../types'

export function useImageNavigation(canvas: Ref<FabricCanvas | null>) {
  const isDragging = ref(false)
  const lastPanPoint = ref({ x: 0, y: 0 })

  // 启用拖拽平移
  const enablePanning = () => {
    if (!canvas.value) return

    // 鼠标按下事件
    canvas.value.on('mouse:down', (opt) => {
      const evt = opt.e as MouseEvent
      if (evt.altKey === true || evt.button === 1) { // Alt键或中键拖拽
        isDragging.value = true
        canvas.value!.selection = false
        lastPanPoint.value.x = evt.clientX
        lastPanPoint.value.y = evt.clientY
        canvas.value!.setCursor('grabbing')
      }
    })

    // 鼠标移动事件
    canvas.value.on('mouse:move', (opt) => {
      if (isDragging.value && canvas.value) {
        const evt = opt.e as MouseEvent
        const vpt = canvas.value.viewportTransform!
        vpt[4] += evt.clientX - lastPanPoint.value.x
        vpt[5] += evt.clientY - lastPanPoint.value.y
        canvas.value.requestRenderAll()
        lastPanPoint.value.x = evt.clientX
        lastPanPoint.value.y = evt.clientY
      }
    })

    // 鼠标抬起事件
    canvas.value.on('mouse:up', () => {
      if (canvas.value) {
        canvas.value.setViewportTransform(canvas.value.viewportTransform!)
        isDragging.value = false
        canvas.value.selection = true
        canvas.value.setCursor('default')
      }
    })

    // 鼠标滚轮缩放
    canvas.value.on('mouse:wheel', (opt) => {
      const evt = opt.e as WheelEvent
      const delta = evt.deltaY
      let zoom = canvas.value!.getZoom()
      zoom *= 0.999 ** delta

      if (zoom > 20) zoom = 20
      if (zoom < 0.01) zoom = 0.01

      const point = new fabric.Point(evt.offsetX, evt.offsetY)
      canvas.value!.zoomToPoint(point, zoom)
      evt.preventDefault()
      evt.stopPropagation()
    })
  }

  // 禁用拖拽平移
  const disablePanning = () => {
    if (!canvas.value) return

    canvas.value.off('mouse:down')
    canvas.value.off('mouse:move') 
    canvas.value.off('mouse:up')
    canvas.value.off('mouse:wheel')
  }

  // 重置视图变换
  const resetViewport = () => {
    if (!canvas.value) return
    
    canvas.value.setViewportTransform([1, 0, 0, 1, 0, 0])
    canvas.value.setZoom(1)
    canvas.value.requestRenderAll()
  }

  // 居中视图
  const centerView = () => {
    if (!canvas.value) return

    const objects = canvas.value.getObjects()
    if (objects.length === 0) return

    const group = new fabric.Group(objects, { left: 0, top: 0 })
    const groupWidth = group.width!
    const groupHeight = group.height!
    
    const canvasWidth = canvas.value.getWidth()
    const canvasHeight = canvas.value.getHeight()
    
    const centerX = (canvasWidth - groupWidth) / 2
    const centerY = (canvasHeight - groupHeight) / 2
    
    canvas.value.setViewportTransform([1, 0, 0, 1, centerX, centerY])
    canvas.value.requestRenderAll()
  }

  // 平移到指定位置
  const panTo = (x: number, y: number) => {
    if (!canvas.value) return
    
    const vpt = canvas.value.viewportTransform!
    vpt[4] = x
    vpt[5] = y
    canvas.value.setViewportTransform(vpt)
    canvas.value.requestRenderAll()
  }

  // 获取当前平移位置
  const getPanPosition = () => {
    if (!canvas.value) return { x: 0, y: 0 }
    
    const vpt = canvas.value.viewportTransform!
    return { x: vpt[4], y: vpt[5] }
  }

  // 键盘导航
  const handleKeyNavigation = (event: KeyboardEvent) => {
    if (!canvas.value) return

    const step = 10
    const vpt = canvas.value.viewportTransform!
    
    switch (event.key) {
      case 'ArrowUp':
        vpt[5] += step
        break
      case 'ArrowDown':
        vpt[5] -= step
        break
      case 'ArrowLeft':
        vpt[4] += step
        break
      case 'ArrowRight':
        vpt[4] -= step
        break
      default:
        return
    }
    
    event.preventDefault()
    canvas.value.setViewportTransform(vpt)
    canvas.value.requestRenderAll()
  }

  // 绑定键盘事件
  const bindKeyboardEvents = () => {
    document.addEventListener('keydown', handleKeyNavigation)
  }

  // 解绑键盘事件
  const unbindKeyboardEvents = () => {
    document.removeEventListener('keydown', handleKeyNavigation)
  }

  // 组件挂载时启用功能
  onMounted(() => {
    enablePanning()
    bindKeyboardEvents()
  })

  // 组件卸载时清理
  onUnmounted(() => {
    disablePanning()
    unbindKeyboardEvents()
  })

  return {
    isDragging: readonly(isDragging),
    enablePanning,
    disablePanning,
    resetViewport,
    centerView,
    panTo,
    getPanPosition,
    handleKeyNavigation
  }
}
