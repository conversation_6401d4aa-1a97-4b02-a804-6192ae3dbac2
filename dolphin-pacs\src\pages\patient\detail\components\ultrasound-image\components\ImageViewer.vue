<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, Warning, Picture } from '@element-plus/icons-vue'
import { useFabricCanvas } from '../composables/useFabricCanvas'
import { useImageZoom } from '../composables/useImageZoom'
import { useImageNavigation } from '../composables/useImageNavigation'
import type { ImageInfo, ToolbarAction, LoadingState } from '../types'

interface Props {
  imageInfo: ImageInfo | null
  width?: number
  height?: number
}

interface Emits {
  (e: 'load', imageInfo: ImageInfo): void
  (e: 'error', error: Error): void
  (e: 'zoom-change', zoom: number): void
}

const props = withDefaults(defineProps<Props>(), {
  width: 800,
  height: 600
})

const emit = defineEmits<Emits>()

// 组件状态
const canvasContainer = ref<HTMLDivElement>()
const loadingState = ref<LoadingState>('idle')
const canvasId = 'ultrasound-canvas'

// 使用组合式函数
const {
  canvas,
  isReady,
  initCanvas,
  loadImage,
  fitToCanvas,
  setZoom: setCanvasZoom,
  getZoom,
  resetView,
  rotateImage,
  resizeCanvas
} = useFabricCanvas(canvasId)

const {
  currentZoom,
  zoomPercentage,
  canZoomIn,
  canZoomOut,
  zoomIn,
  zoomOut,
  resetZoom,
  actualSize,
  handleWheelZoom
} = useImageZoom({ min: 0.1, max: 10, step: 0.25, default: 1 })

const { enablePanning } = useImageNavigation(canvas)

// 监听缩放变化
watch(currentZoom, (newZoom) => {
  setCanvasZoom(newZoom)
  emit('zoom-change', newZoom)
})

// 监听图像信息变化
watch(() => props.imageInfo, async (newImageInfo) => {
  if (newImageInfo && isReady.value) {
    await loadImageData(newImageInfo)
  }
}, { immediate: true })

// 加载图像数据
const loadImageData = async (imageInfo: ImageInfo) => {
  try {
    loadingState.value = 'loading'
    await loadImage(imageInfo)
    loadingState.value = 'loaded'
    resetZoom()
    emit('load', imageInfo)
  } catch (error) {
    loadingState.value = 'error'
    const errorObj = error instanceof Error ? error : new Error('图像加载失败')
    ElMessage.error(errorObj.message)
    emit('error', errorObj)
  }
}

// 处理工具栏操作
const handleToolbarAction = (action: ToolbarAction) => {
  switch (action) {
    case 'zoom-in':
      zoomIn()
      break
    case 'zoom-out':
      zoomOut()
      break
    case 'zoom-fit':
      fitToCanvas()
      resetZoom()
      break
    case 'zoom-actual':
      actualSize()
      break
    case 'rotate-left':
      rotateImage(-90)
      break
    case 'rotate-right':
      rotateImage(90)
      break
    case 'reset':
      resetView()
      resetZoom()
      break
    case 'fullscreen':
      // 全屏功能由父组件处理
      break
  }
}

// 处理容器大小变化
const handleResize = () => {
  if (canvasContainer.value && canvas.value) {
    const { clientWidth, clientHeight } = canvasContainer.value
    resizeCanvas(clientWidth, clientHeight)
  }
}

// 初始化
onMounted(async () => {
  await nextTick()
  await initCanvas()
  
  // 设置初始画布大小
  if (canvasContainer.value) {
    const { clientWidth, clientHeight } = canvasContainer.value
    resizeCanvas(clientWidth, clientHeight)
  }

  // 如果有图像信息，立即加载
  if (props.imageInfo) {
    await loadImageData(props.imageInfo)
  }

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

// 清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 暴露方法给父组件
defineExpose({
  fitToCanvas,
  resetView,
  zoomIn,
  zoomOut,
  actualSize,
  getZoom
})
</script>

<template>
  <div class="image-viewer">
    <!-- 画布容器 -->
    <div 
      ref="canvasContainer" 
      class="canvas-container"
      :style="{ width: width + 'px', height: height + 'px' }"
    >
      <!-- Fabric.js Canvas -->
      <canvas 
        :id="canvasId"
        class="fabric-canvas"
      />

      <!-- 加载状态 -->
      <div v-if="loadingState === 'loading'" class="loading-overlay">
        <el-icon class="loading-icon is-loading"><Loading /></el-icon>
        <span class="loading-text">正在加载图像...</span>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="loadingState === 'error'" class="error-overlay">
        <el-icon class="error-icon"><Warning /></el-icon>
        <span class="error-text">图像加载失败</span>
      </div>

      <!-- 无图像状态 -->
      <div v-else-if="!imageInfo" class="empty-overlay">
        <el-icon class="empty-icon"><Picture /></el-icon>
        <span class="empty-text">请选择要查看的超声图像</span>
      </div>
    </div>

    <!-- 操作提示 -->
    <div class="operation-tips">
      <span class="tip-text">
        💡 提示：按住Alt键拖拽可平移图像，滚轮可缩放
      </span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.image-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;

  .canvas-container {
    position: relative;
    flex: 1;
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    overflow: hidden;
    background: #f8f9fa;

    .fabric-canvas {
      display: block;
    }

    .loading-overlay,
    .error-overlay,
    .empty-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(2px);
    }

    .loading-overlay {
      .loading-text {
        margin-top: 12px;
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }

    .error-overlay {
      .error-icon {
        font-size: 48px;
        color: var(--el-color-danger);
        margin-bottom: 12px;
      }

      .error-text {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }

    .empty-overlay {
      .empty-icon {
        font-size: 48px;
        color: var(--el-text-color-placeholder);
        margin-bottom: 12px;
      }

      .empty-text {
        font-size: 14px;
        color: var(--el-text-color-placeholder);
      }
    }
  }

  .operation-tips {
    padding: 8px 12px;
    background: var(--el-bg-color-page);
    border-radius: 4px;
    margin-top: 8px;

    .tip-text {
      font-size: 12px;
      color: var(--el-text-color-regular);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .image-viewer {
    .operation-tips {
      padding: 6px 8px;

      .tip-text {
        font-size: 11px;
      }
    }
  }
}
</style>
